#!/usr/bin/env python3
"""
Test script for verifying streaming functionality in the MCP Financial Analyzer WebSocket interface.
"""

import asyncio
import json
import websockets
import argparse
import time
from typing import List, Dict, Any
from urllib.parse import quote


class StreamingTest:
    """Test class for streaming WebSocket functionality."""
    
    def __init__(self, host: str = "localhost", port: int = 8000):
        self.host = host
        self.port = port
        self.results = {}
        
    async def test_endpoint(self, endpoint: str, company: str = "Apple Inc."):
        """Test streaming for a specific endpoint."""
        user_id = f"test_user_{int(time.time())}"
        uri = f"ws://{self.host}:{self.port}/ws/{endpoint}/{user_id}?company={quote(company)}"
        
        print(f"\n🧪 Testing streaming for {endpoint.upper()} endpoint")
        print(f"🔗 Connecting to {uri}")
        
        try:
            async with websockets.connect(uri) as websocket:
                print(f"✅ Connected to {endpoint} endpoint")
                
                # Wait for welcome message
                welcome = await websocket.recv()
                welcome_data = json.loads(welcome)
                print(f"👋 Received welcome message: {welcome_data['message']}")
                
                # Send test message with streaming enabled
                test_message = f"Analyze {company} with streaming"
                print(f"📤 Sending message: {test_message}")
                await websocket.send(json.dumps({
                    "message": test_message,
                    "streaming": True
                }))
                
                # Track streaming metrics
                stream_started = False
                stream_chunks = []
                stream_ended = False
                start_time = time.time()
                chunk_times = []
                
                # Listen for streaming responses
                try:
                    while True:
                        if time.time() - start_time > 120:  # 2 minute timeout
                            print("⚠️ Test timeout reached")
                            break
                            
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                            data = json.loads(response)
                            
                            if data["type"] == "stream_start":
                                stream_started = True
                                print(f"🔄 Stream started: {data['message']}")
                                
                            elif data["type"] == "stream_chunk":
                                chunk_time = time.time()
                                chunk_times.append(chunk_time)
                                stream_chunks.append(data["message"])
                                if len(stream_chunks) == 1 or len(stream_chunks) % 5 == 0:
                                    print(f"📦 Received chunk #{len(stream_chunks)}: {data['message'][:30]}...")
                                
                            elif data["type"] == "stream_end":
                                stream_ended = True
                                print(f"✅ Stream ended: {data['message']}")
                                break
                                
                            elif data["type"] == "error":
                                print(f"❌ Error: {data['message']}")
                                break
                        
                        except asyncio.TimeoutError:
                            if stream_started and not stream_ended:
                                print("⏳ Waiting for more chunks...")
                            else:
                                print("⚠️ Timeout waiting for response")
                                break
                
                except Exception as e:
                    print(f"❌ Error during streaming: {e}")
                
                # Calculate metrics
                end_time = time.time()
                total_time = end_time - start_time
                total_content = "".join(stream_chunks)
                chunk_count = len(stream_chunks)
                
                # Store results
                self.results[endpoint] = {
                    "success": stream_started and stream_ended,
                    "stream_started": stream_started,
                    "stream_ended": stream_ended,
                    "chunk_count": chunk_count,
                    "total_time": total_time,
                    "content_length": len(total_content),
                    "avg_chunk_interval": self._calculate_avg_interval(chunk_times) if len(chunk_times) > 1 else 0
                }
                
                # Print summary
                print(f"\n📊 {endpoint.upper()} ENDPOINT SUMMARY:")
                print(f"   Stream started: {'✅' if stream_started else '❌'}")
                print(f"   Stream ended: {'✅' if stream_ended else '❌'}")
                print(f"   Chunks received: {chunk_count}")
                print(f"   Total time: {total_time:.2f} seconds")
                print(f"   Content length: {len(total_content)} characters")
                if len(chunk_times) > 1:
                    print(f"   Avg chunk interval: {self._calculate_avg_interval(chunk_times):.3f} seconds")
                
                return self.results[endpoint]["success"]
                
        except Exception as e:
            print(f"❌ Connection error: {e}")
            self.results[endpoint] = {"success": False, "error": str(e)}
            return False
    
    def _calculate_avg_interval(self, timestamps: List[float]) -> float:
        """Calculate average interval between timestamps."""
        if len(timestamps) < 2:
            return 0
        intervals = [timestamps[i] - timestamps[i-1] for i in range(1, len(timestamps))]
        return sum(intervals) / len(intervals)
    
    async def run_all_tests(self, company: str = "Apple Inc."):
        """Run tests for all endpoints."""
        endpoints = ["research", "analyze", "report", "full_analysis"]
        results = []
        
        print("\n🚀 STARTING STREAMING TESTS")
        print("=" * 60)
        
        for endpoint in endpoints:
            success = await self.test_endpoint(endpoint, company)
            results.append(success)
            # Add a delay between tests
            await asyncio.sleep(2)
        
        print("\n" + "=" * 60)
        print("📋 OVERALL TEST RESULTS:")
        
        all_passed = all(results)
        for i, endpoint in enumerate(endpoints):
            status = "✅ PASS" if results[i] else "❌ FAIL"
            print(f"   {endpoint.ljust(15)}: {status}")
        
        print("\n🏁 FINAL RESULT: " + ("✅ ALL TESTS PASSED" if all_passed else "❌ SOME TESTS FAILED"))
        return all_passed


async def main():
    """Main function to run the tests."""
    parser = argparse.ArgumentParser(description="Test streaming functionality")
    parser.add_argument("--endpoint", choices=["research", "analyze", "report", "full_analysis"], 
                       help="Specific endpoint to test (default: test all)")
    parser.add_argument("--company", default="Apple Inc.", help="Company to analyze")
    parser.add_argument("--host", default="localhost", help="WebSocket server host")
    parser.add_argument("--port", type=int, default=8000, help="WebSocket server port")
    
    args = parser.parse_args()
    
    tester = StreamingTest(args.host, args.port)
    
    if args.endpoint:
        await tester.test_endpoint(args.endpoint, args.company)
    else:
        await tester.run_all_tests(args.company)


if __name__ == "__main__":
    print("🔍 MCP Financial Analyzer Streaming Tests")
    print("=" * 60)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
