# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **FastAPI WebSocket implementation** of the MCP Financial Analyzer that provides real-time financial analysis through specialized AI agents. It demonstrates sophisticated multi-agent workflows using the mcp-agent framework with WebSocket-based real-time communication.

## Common Development Commands

**Server Management:**
- `python main.py` - Start the FastAPI WebSocket server on port 8000
- `uvicorn main:app --reload --host 0.0.0.0 --port 8000` - Start with auto-reload for development

**Testing:**
- `python financial_websocket_client.py --test-all --company "Tesla Inc."` - Test all endpoints
- `python financial_websocket_client.py --endpoint research --company "Apple" --interactive` - Interactive testing
- `python debug_client.py` - Debug client for WebSocket connections

**Dependencies:**
- `pip install -r requirements.txt` - Install Python dependencies
- `npm install -g g-search-mcp @modelcontextprotocol/server-filesystem` - Install MCP servers

**Monitoring:**
- View logs at: `logs/mcp-agent-{timestamp}.jsonl` and `logs/main_debug.log`
- Health check: `curl http://localhost:8000/health`
- List sessions: `curl http://localhost:8000/sessions`

## Core Architecture

### WebSocket Endpoints and Specialized Agents

The application exposes four specialized WebSocket endpoints, each with its own agent type:

**Research Endpoint** (`/ws/research/{user_id}?company={company_name}`):
- **Agent**: Research agent with Google Search and web fetch capabilities
- **Purpose**: Gather comprehensive financial data (stock prices, earnings, news)
- **MCP Servers**: `g-search`, `fetch`

**Analyze Endpoint** (`/ws/analyze/{user_id}?company={company_name}`):
- **Agent**: Financial analyst agent for data analysis and insights
- **Purpose**: Analyze financial data and provide structured insights
- **MCP Servers**: `fetch`

**Report Endpoint** (`/ws/report/{user_id}?company={company_name}`):
- **Agent**: Report writer agent with filesystem access
- **Purpose**: Generate professional financial reports in markdown format
- **MCP Servers**: `filesystem`
- **Output**: Saves reports to `company_reports/` directory with timestamp

**Full Analysis Endpoint** (`/ws/full_analysis/{user_id}?company={company_name}`):
- **Architecture**: Orchestrator workflow coordinating all three agent types
- **Quality Control**: EvaluatorOptimizerLLM ensures EXCELLENT quality research
- **Process**: Research → Quality Validation → Analysis → Report Generation

### Session Management (`session_manager.py`)

**FinancialSessionManager**:
- **Session Types**: `RESEARCH`, `ANALYZE`, `REPORT`, `FULL_ANALYSIS`
- **Session Lifecycle**: Automatic initialization, persistent state, cleanup after 2 hours of inactivity
- **Agent Integration**: Creates specialized agent instances per session type
- **Streaming Support**: Custom `StreamingOpenAILLM` wrapper for real-time responses

**FinancialSession**:
- **State Management**: Maintains conversation history, last activity tracking
- **Agent Coordination**: Manages MCP app lifecycle and agent initialization
- **Dual Processing**: Supports both streaming and non-streaming message processing

### WebSocket Communication Pattern (`main.py`)

**Message Flow**:
1. **Connection**: Client connects with user_id and company parameter
2. **Session Creation**: `session_manager.get_or_create_session()` initializes appropriate agents
3. **Message Processing**: Incoming messages routed to session-specific agents
4. **Streaming Response**: Real-time chunks sent via `stream_callback`
5. **Cleanup**: Automatic session cleanup on disconnect

**Message Format**:
```json
// Client to Server
{"message": "request", "streaming": true}

// Server to Client
{"type": "stream_start|stream_chunk|stream_end|system|error", "message": "content", "user_id": "id"}
```

## Key Implementation Patterns

### Agent Factory Integration

Agents are created using factory functions from the parent directory (`../agents/`):
- `create_research_agent(company_name)` - Research agent with Google Search
- `create_analyst_agent(company_name)` - Financial analysis agent  
- `create_report_writer(company_name, output_path)` - Report generation agent

### Streaming Response Implementation

**Custom Streaming Wrapper** (`StreamingOpenAILLM`):
- Wraps `OpenAIAugmentedLLM` to provide streaming capabilities
- Simulates word-by-word streaming for real-time user experience
- Fallback to non-streaming if streaming fails

### Quality Control Workflow

**EvaluatorOptimizerLLM Integration** (Full Analysis only):
- **Research Evaluator**: Validates research quality on accuracy, completeness, specificity
- **Quality Standards**: Requires `EXCELLENT` rating before proceeding
- **Iterative Improvement**: Automatically refines research until quality criteria met

## Configuration Management

### MCP Configuration (`mcp_agent.config.yaml`)

**Execution Engine**: AsyncIO for concurrent WebSocket handling
**LLM Providers**: 
- Primary: VLLM with Qwen/Qwen3-32B (`feature/vllm-support` branch)
- Fallback: OpenAI GPT-4o-mini

**MCP Servers**:
- `fetch`: Web content retrieval via `uvx mcp-server-fetch`
- `g-search`: Google Search via `npx -y g-search-mcp`
- `filesystem`: File operations via `npx -y @modelcontextprotocol/server-filesystem`

### Secrets Configuration (`mcp_agent.secrets.yaml`)

Configure API keys for LLM providers. VLLM typically doesn't require API keys for local deployment.

## Development Patterns

### Adding New Endpoint Types

1. **Define SessionType**: Add new enum value in `session_manager.py:SessionType`
2. **Agent Initialization**: Add logic in `FinancialSession._initialize_agents()`
3. **WebSocket Endpoint**: Create new endpoint in `main.py` using `handle_websocket_connection()`
4. **HTML Interface**: Update embedded HTML to include new endpoint controls

### Error Handling Strategy

**WebSocket Level**: Graceful disconnection handling, connection error recovery
**Session Level**: Agent initialization failure recovery, automatic session cleanup
**Message Level**: JSON parsing errors, agent processing errors with user feedback

### Logging and Debugging

**Structured Logging**: JSONL format in `logs/` directory for analysis
**Debug Mode**: Detailed logging with function-level tracing
**Connection Tracking**: Each WebSocket connection has unique identifier for tracing

## Important Implementation Notes

### Agent Lifecycle Management

- **Context Managers**: All agents use `async with` pattern for proper resource cleanup
- **MCP App Integration**: Each session creates its own MCP app instance
- **Filesystem Path**: Current directory added to filesystem server args dynamically

### Real-time Streaming Considerations

- **Chunk Size**: Streaming sends 3-5 words per chunk with 0.1s delay
- **Fallback Strategy**: Automatic fallback to non-streaming if streaming fails
- **Full Analysis**: Orchestrator workflow provides progress updates during complex operations

### Output Management

**Report Generation**: 
- **Directory**: `company_reports/` with automatic creation
- **Naming**: `{company_name}_report_{timestamp}.md` format
- **Content**: Professional markdown reports with structured sections

**Session Persistence**:
- **In-Memory**: Session state maintained in memory only
- **History**: Conversation history preserved per session
- **Cleanup**: Automatic cleanup after 2 hours of inactivity

This WebSocket implementation showcases advanced mcp-agent capabilities including real-time communication, specialized agent coordination, and quality-controlled multi-agent workflows.