import json
from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
import uvicorn
from contextlib import asynccontextmanager

from session_manager import FinancialSessionManager, SessionType


# Global session manager
session_manager = FinancialSessionManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Startup and shutdown events for the FastAPI application."""
    # Startup
    await session_manager.initialize()
    yield
    # Shutdown
    await session_manager.cleanup()


app = FastAPI(title="MCP Financial Analyzer WebSocket Server", lifespan=lifespan)


@app.get("/")
async def get():
    """Serve a comprehensive HTML page for testing financial analysis WebSocket connections."""
    return HTMLResponse("""
<!DOCTYPE html>
<html>
<head>
    <title>MCP Financial Analyzer WebSocket Interface</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .endpoint-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .endpoint-title { font-size: 18px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .controls { display: flex; gap: 10px; align-items: center; margin-bottom: 15px; flex-wrap: wrap; }
        .controls input, .controls button { padding: 8px 12px; border: 1px solid #ccc; border-radius: 4px; }
        .controls button { background: #007bff; color: white; cursor: pointer; }
        .controls button:hover { background: #0056b3; }
        .controls button:disabled { background: #ccc; cursor: not-allowed; }
        .status { padding: 5px 10px; border-radius: 3px; font-weight: bold; }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .messages { border: 1px solid #ccc; height: 300px; overflow-y: auto; padding: 10px; margin: 10px 0; background: #fafafa; }
        .message { margin: 5px 0; padding: 8px; border-radius: 4px; }
        .user { background-color: #e3f2fd; border-left: 4px solid #2196f3; }
        .assistant { background-color: #f1f8e9; border-left: 4px solid #4caf50; }
        .system { background-color: #fff3e0; border-left: 4px solid #ff9800; }
        .error { background-color: #ffebee; border-left: 4px solid #f44336; }
        .input-section { display: flex; gap: 10px; margin-top: 10px; }
        .input-section input { flex: 1; padding: 10px; }
        .input-section button { padding: 10px 20px; }
        .description { font-size: 14px; color: #666; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MCP Financial Analyzer WebSocket Interface</h1>
            <p>Real-time financial analysis with specialized AI agents</p>
        </div>

        <!-- Research Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">🔍 Research Endpoint</div>
            <div class="description">Gather comprehensive financial data about a company using AI-powered research agents.</div>
            <div class="controls">
                <input type="text" id="researchUserId" value="research_user" placeholder="User ID" />
                <input type="text" id="researchCompany" value="Apple Inc." placeholder="Company Name" />
                <button onclick="connectResearch()">Connect</button>
                <button onclick="disconnectResearch()">Disconnect</button>
                <span id="researchStatus" class="status disconnected">Disconnected</span>
            </div>
            <div id="researchMessages" class="messages"></div>
            <div class="input-section">
                <input type="text" id="researchInput" placeholder="Ask for research on the company..." onkeypress="handleKeyPress(event, 'research')" />
                <button onclick="sendMessage('research')">Send</button>
            </div>
        </div>

        <!-- Analyze Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">📊 Analyze Endpoint</div>
            <div class="description">Perform detailed financial analysis on provided data or company information.</div>
            <div class="controls">
                <input type="text" id="analyzeUserId" value="analyze_user" placeholder="User ID" />
                <input type="text" id="analyzeCompany" value="Apple Inc." placeholder="Company Name" />
                <button onclick="connectAnalyze()">Connect</button>
                <button onclick="disconnectAnalyze()">Disconnect</button>
                <span id="analyzeStatus" class="status disconnected">Disconnected</span>
            </div>
            <div id="analyzeMessages" class="messages"></div>
            <div class="input-section">
                <input type="text" id="analyzeInput" placeholder="Provide data to analyze or ask for analysis..." onkeypress="handleKeyPress(event, 'analyze')" />
                <button onclick="sendMessage('analyze')">Send</button>
            </div>
        </div>

        <!-- Report Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">📄 Report Endpoint</div>
            <div class="description">Generate professional financial reports based on analysis and research data.</div>
            <div class="controls">
                <input type="text" id="reportUserId" value="report_user" placeholder="User ID" />
                <input type="text" id="reportCompany" value="Apple Inc." placeholder="Company Name" />
                <button onclick="connectReport()">Connect</button>
                <button onclick="disconnectReport()">Disconnect</button>
                <span id="reportStatus" class="status disconnected">Disconnected</span>
            </div>
            <div id="reportMessages" class="messages"></div>
            <div class="input-section">
                <input type="text" id="reportInput" placeholder="Request a specific type of report..." onkeypress="handleKeyPress(event, 'report')" />
                <button onclick="sendMessage('report')">Send</button>
            </div>
        </div>

        <!-- Full Analysis Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">🎯 Full Analysis Endpoint</div>
            <div class="description">Complete end-to-end financial analysis: research → analyze → report generation.</div>
            <div class="controls">
                <input type="text" id="fullUserId" value="full_user" placeholder="User ID" />
                <input type="text" id="fullCompany" value="Apple Inc." placeholder="Company Name" />
                <button onclick="connectFull()">Connect</button>
                <button onclick="disconnectFull()">Disconnect</button>
                <span id="fullStatus" class="status disconnected">Disconnected</span>
            </div>
            <div id="fullMessages" class="messages"></div>
            <div class="input-section">
                <input type="text" id="fullInput" placeholder="Request complete analysis of a company..." onkeypress="handleKeyPress(event, 'full')" />
                <button onclick="sendMessage('full')">Send</button>
            </div>
        </div>
    </div>

    <script>
        let connections = {
            research: null,
            analyze: null,
            report: null,
            full: null
        };

        function connect(endpoint) {
            const userId = document.getElementById(`${endpoint}UserId`).value || `${endpoint}_user`;
            const company = document.getElementById(`${endpoint}Company`).value || 'Apple Inc.';
            
            const ws = new WebSocket(`ws://localhost:8000/ws/${endpoint}/${userId}?company=${encodeURIComponent(company)}`);
            connections[endpoint] = ws;
            
            ws.onopen = function(event) {
                document.getElementById(`${endpoint}Status`).textContent = 'Connected';
                document.getElementById(`${endpoint}Status`).className = 'status connected';
                addMessage(endpoint, 'system', `Connected to ${endpoint} endpoint for ${company}`);
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                const messageType = data.type || 'assistant';
                addMessage(endpoint, messageType, data.message);
            };
            
            ws.onclose = function(event) {
                document.getElementById(`${endpoint}Status`).textContent = 'Disconnected';
                document.getElementById(`${endpoint}Status`).className = 'status disconnected';
                addMessage(endpoint, 'system', `Disconnected from ${endpoint} endpoint`);
                connections[endpoint] = null;
            };
            
            ws.onerror = function(error) {
                addMessage(endpoint, 'error', `WebSocket error: ${error}`);
            };
        }

        function disconnect(endpoint) {
            if (connections[endpoint]) {
                connections[endpoint].close();
            }
        }

        function sendMessage(endpoint) {
            const input = document.getElementById(`${endpoint}Input`);
            const message = input.value.trim();
            if (message && connections[endpoint] && connections[endpoint].readyState === WebSocket.OPEN) {
                connections[endpoint].send(JSON.stringify({message: message}));
                addMessage(endpoint, 'user', message);
                input.value = '';
            }
        }

        function handleKeyPress(event, endpoint) {
            if (event.key === 'Enter') {
                sendMessage(endpoint);
            }
        }

        function addMessage(endpoint, type, message) {
            const messagesDiv = document.getElementById(`${endpoint}Messages`);
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Connection functions for each endpoint
        function connectResearch() { connect('research'); }
        function disconnectResearch() { disconnect('research'); }
        function connectAnalyze() { connect('analyze'); }
        function disconnectAnalyze() { disconnect('analyze'); }
        function connectReport() { connect('report'); }
        function disconnectReport() { disconnect('report'); }
        function connectFull() { connect('full'); }
        function disconnectFull() { disconnect('full'); }
    </script>
</body>
</html>
    """)


async def handle_websocket_connection(websocket: WebSocket, user_id: str, session_type: SessionType, company_name: str):
    """Common WebSocket connection handler for all endpoints."""
    await websocket.accept()

    try:
        # Get or create user session
        user_session = await session_manager.get_or_create_session(user_id, session_type, company_name)

        # Send welcome message
        await websocket.send_text(
            json.dumps(
                {
                    "type": "system",
                    "message": f"Welcome to {session_type.value} analysis for {company_name}! Session ID: {user_session.session_id}",
                    "user_id": user_id,
                    "session_id": user_session.session_id,
                    "company_name": company_name,
                }
            )
        )

        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message_data = json.loads(data)
                user_message = message_data.get("message", "")

                if not user_message:
                    continue

                # Send progress message
                await websocket.send_text(
                    json.dumps(
                        {
                            "type": "progress",
                            "message": f"Processing {session_type.value} request...",
                        }
                    )
                )

                # Process message through appropriate agent
                response = await user_session.process_message(user_message)

                # Send response back to client
                await websocket.send_text(
                    json.dumps(
                        {
                            "type": "result",
                            "message": response,
                            "user_id": user_id,
                            "session_id": user_session.session_id,
                            "company_name": company_name,
                        }
                    )
                )

            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await websocket.send_text(
                    json.dumps({"type": "error", "message": "Invalid JSON format"})
                )
            except Exception as e:
                await websocket.send_text(
                    json.dumps({"type": "error", "message": f"An error occurred: {str(e)}"})
                )

    except Exception as e:
        await websocket.send_text(
            json.dumps({"type": "error", "message": f"Session error: {str(e)}"})
        )
    finally:
        # Clean up session if needed
        await session_manager.cleanup_session(user_id, session_type)


@app.websocket("/ws/research/{user_id}")
async def research_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for financial research functionality."""
    company = websocket.query_params.get("company", "Apple Inc.")
    await handle_websocket_connection(websocket, user_id, SessionType.RESEARCH, company)


@app.websocket("/ws/analyze/{user_id}")
async def analyze_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for financial analysis operations."""
    company = websocket.query_params.get("company", "Apple Inc.")
    await handle_websocket_connection(websocket, user_id, SessionType.ANALYZE, company)


@app.websocket("/ws/report/{user_id}")
async def report_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for generating financial reports."""
    company = websocket.query_params.get("company", "Apple Inc.")
    await handle_websocket_connection(websocket, user_id, SessionType.REPORT, company)


@app.websocket("/ws/full_analysis/{user_id}")
async def full_analysis_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for comprehensive end-to-end financial analysis."""
    company = websocket.query_params.get("company", "Apple Inc.")
    await handle_websocket_connection(websocket, user_id, SessionType.FULL_ANALYSIS, company)


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy", 
        "active_sessions": len(session_manager.sessions),
        "endpoints": ["research", "analyze", "report", "full_analysis"]
    }


@app.get("/sessions")
async def list_sessions():
    """List active sessions."""
    sessions_info = {}
    for session_key, session in session_manager.sessions.items():
        sessions_info[session_key] = {
            "user_id": session.user_id,
            "session_type": session.session_type.value,
            "company_name": session.company_name,
            "created_at": session.created_at.isoformat(),
            "last_activity": session.last_activity.isoformat(),
            "message_count": len(session.message_history),
        }
    
    return {
        "active_sessions": sessions_info,
        "total_sessions": len(session_manager.sessions),
    }


@app.get("/sessions/{user_id}/{session_type}")
async def get_session_info(user_id: str, session_type: str):
    """Get information about a specific session."""
    try:
        session_type_enum = SessionType(session_type)
        session_info = session_manager.get_session_info(user_id, session_type_enum)
        if session_info:
            return session_info
        else:
            return {"error": "Session not found"}
    except ValueError:
        return {"error": f"Invalid session type: {session_type}"}


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
