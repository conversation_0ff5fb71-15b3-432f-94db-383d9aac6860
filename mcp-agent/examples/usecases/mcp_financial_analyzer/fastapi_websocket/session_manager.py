import asyncio
import os
import uuid
from typing import Dict, Optional, AsyncGenerator, Callable
from datetime import datetime
from enum import Enum

from mcp_agent.app import <PERSON><PERSON><PERSON>
from mcp_agent.agents.agent import Agent
from mcp_agent.workflows.llm.augmented_llm_openai import OpenAIAugmentedLLM
from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAugmentedLLM
from mcp_agent.workflows.orchestrator.orchestrator import Orchestrator
from mcp_agent.workflows.llm.augmented_llm import RequestParams
from mcp_agent.workflows.evaluator_optimizer.evaluator_optimizer import (
    EvaluatorOptimizerLLM,
    QualityRating,
)

# Import agent factories from parent directory
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from agents.research_agent import create_research_agent
from agents.analyst_agent import create_analyst_agent
from agents.report_writer import create_report_writer


class SessionType(Enum):
    """Types of financial analysis sessions."""
    RESEARCH = "research"
    ANALYZE = "analyze"
    REPORT = "report"
    FULL_ANALYSIS = "full_analysis"


class StreamingOpenAILLM:
    """Wrapper for OpenAIAugmentedLLM that provides streaming capabilities."""

    def __init__(self, llm: OpenAIAugmentedLLM):
        self.llm = llm

    async def generate_str_streaming(
        self,
        message: str,
        request_params: RequestParams | None = None,
        stream_callback: Callable[[str], None] | None = None
    ) -> str:
        """Generate a streaming response using OpenAI's streaming API."""
        try:
            # For now, let's use a simple approach that simulates streaming
            # by calling the regular LLM and then streaming the response word by word

            if stream_callback:
                stream_callback("Starting analysis...\n\n")

            # Get the full response first
            full_response = await self.llm.generate_str(message, request_params)

            # Stream the response word by word to simulate real streaming
            if stream_callback and full_response:
                words = full_response.split()
                current_text = ""

                for i, word in enumerate(words):
                    current_text += word + " "

                    # Send chunks of 3-5 words at a time
                    if (i + 1) % 4 == 0 or i == len(words) - 1:
                        stream_callback(current_text[len(current_text) - len(" ".join(words[max(0, i-3):i+1])) - 1:])
                        # Small delay to simulate real streaming
                        await asyncio.sleep(0.1)

            return full_response

        except Exception as e:
            # Fallback to non-streaming if streaming fails
            error_msg = f"[Streaming failed, using fallback: {str(e)}]\n"
            if stream_callback:
                stream_callback(error_msg)
            return await self.llm.generate_str(message, request_params)


class FinancialSession:
    """Represents a financial analysis session with specialized agent integration."""

    def __init__(self, user_id: str, session_id: str, session_type: SessionType):
        self.user_id = user_id
        self.session_id = session_id
        self.session_type = session_type
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.message_history = []
        self.company_name = None

        # MCP agent components
        self.mcp_app: Optional[MCPApp] = None
        self.agent_app = None

        # Specialized agents based on session type
        self.research_agent = None
        self.analyst_agent = None
        self.report_writer = None
        self.orchestrator = None

        # LLM components
        self.llm = None
        self.streaming_llm = None

    async def initialize(self, company_name: str):
        """Initialize the financial analysis session with appropriate agents."""
        try:
            self.company_name = company_name
            
            # Create MCP app for this session
            self.mcp_app = MCPApp(name=f"financial_websocket_{self.session_type.value}_{self.user_id}")

            # Start the MCP app
            self.agent_app = await self.mcp_app.run().__aenter__()

            # Get context and logger
            context = self.agent_app.context
            logger = self.agent_app.logger

            # Add current directory to filesystem server args
            context.config.mcp.servers["filesystem"].args.extend([os.getcwd()])

            # Initialize agents based on session type
            await self._initialize_agents(company_name, logger)

            logger.info(f"Financial session initialized for user {self.user_id}, type: {self.session_type.value}")

        except Exception as e:
            if self.agent_app:
                await self.agent_app.__aexit__(None, None, None)
            raise e

    async def _initialize_agents(self, company_name: str, logger):
        """Initialize appropriate agents based on session type."""
        
        if self.session_type == SessionType.RESEARCH:
            # Research-only session
            self.research_agent = create_research_agent(company_name)
            await self.research_agent.__aenter__()
            self.llm = await self.research_agent.attach_llm(OpenAIAugmentedLLM)
            self.streaming_llm = StreamingOpenAILLM(self.llm)

        elif self.session_type == SessionType.ANALYZE:
            # Analysis-only session
            self.analyst_agent = create_analyst_agent(company_name)
            await self.analyst_agent.__aenter__()
            self.llm = await self.analyst_agent.attach_llm(OpenAIAugmentedLLM)
            self.streaming_llm = StreamingOpenAILLM(self.llm)

        elif self.session_type == SessionType.REPORT:
            # Report generation session
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"{company_name.lower().replace(' ', '_')}_report_{timestamp}.md"
            output_path = os.path.join("company_reports", output_file)

            self.report_writer = create_report_writer(company_name, output_path)
            await self.report_writer.__aenter__()
            self.llm = await self.report_writer.attach_llm(OpenAIAugmentedLLM)
            self.streaming_llm = StreamingOpenAILLM(self.llm)
            
        elif self.session_type == SessionType.FULL_ANALYSIS:
            # Full analysis with orchestrator
            await self._initialize_full_analysis(company_name, logger)

    async def _initialize_full_analysis(self, company_name: str, logger):
        """Initialize full analysis session with orchestrator."""
        # Create all agents
        self.research_agent = create_research_agent(company_name)
        
        # Research evaluator
        research_evaluator = Agent(
            name="research_evaluator",
            instruction=f"""You are an expert research evaluator specializing in financial data quality.
            
            Evaluate the research data on {company_name} based on these criteria:
            
            1. Accuracy: Are facts properly cited with source URLs? Are numbers precise?
            2. Completeness: Is all required information present? (stock price, earnings data, recent news)
            3. Specificity: Are exact figures provided rather than generalizations?
            4. Clarity: Is the information organized and easy to understand?
            
            For each criterion, provide a rating:
            - EXCELLENT: Exceeds requirements, highly reliable
            - GOOD: Meets all requirements, reliable
            - FAIR: Missing some elements but usable
            - POOR: Missing critical information, not usable
            
            Provide an overall quality rating and specific feedback on what needs improvement.
            If any critical financial data is missing (stock price, earnings figures), the overall
            rating should not exceed FAIR.""",
        )

        # Create the research quality controller
        research_quality_controller = EvaluatorOptimizerLLM(
            optimizer=self.research_agent,
            evaluator=research_evaluator,
            llm_factory=OpenAIAugmentedLLM,
            min_rating=QualityRating.EXCELLENT,
        )

        self.analyst_agent = create_analyst_agent(company_name)

        # Create report writer with timestamped output
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"{company_name.lower().replace(' ', '_')}_report_{timestamp}.md"
        output_path = os.path.join("company_reports", output_file)
        self.report_writer = create_report_writer(company_name, output_path)

        # Create orchestrator
        self.orchestrator = Orchestrator(
            llm_factory=OpenAIAugmentedLLM,
            available_agents=[
                research_quality_controller,
                self.analyst_agent,
                self.report_writer,
            ],
            plan_type="full",
        )

    async def process_message(self, message: str) -> str:
        """Process a user message through the appropriate financial analysis workflow."""
        try:
            # Update last activity
            self.last_activity = datetime.now()

            # Add to message history
            self.message_history.append(
                {
                    "role": "user",
                    "content": message,
                    "timestamp": self.last_activity.isoformat(),
                }
            )

            # Process based on session type
            response = await self._process_by_type(message)

            # Add response to history
            self.message_history.append(
                {
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat(),
                }
            )

            return response

        except Exception as e:
            error_msg = f"Error processing financial analysis: {str(e)}"
            self.message_history.append(
                {
                    "role": "error",
                    "content": error_msg,
                    "timestamp": datetime.now().isoformat(),
                }
            )
            return error_msg

    async def process_message_streaming(
        self,
        message: str,
        stream_callback: Callable[[str], None]
    ) -> str:
        """Process a user message with streaming response."""
        try:
            # Update last activity
            self.last_activity = datetime.now()

            # Add to message history
            self.message_history.append(
                {
                    "role": "user",
                    "content": message,
                    "timestamp": self.last_activity.isoformat(),
                }
            )

            # Process based on session type with streaming
            response = await self._process_by_type_streaming(message, stream_callback)

            # Add response to history
            self.message_history.append(
                {
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat(),
                }
            )

            return response

        except Exception as e:
            error_msg = f"Error processing financial analysis: {str(e)}"
            self.message_history.append(
                {
                    "role": "error",
                    "content": error_msg,
                    "timestamp": datetime.now().isoformat(),
                }
            )
            stream_callback(error_msg)
            return error_msg

    async def _process_by_type(self, message: str) -> str:
        """Process message based on session type."""

        if self.session_type == SessionType.RESEARCH:
            if not self.llm:
                return "Error: Research agent not initialized"
            return await self.llm.generate_str(message=message)

        elif self.session_type == SessionType.ANALYZE:
            if not self.llm:
                return "Error: Analyst agent not initialized"
            return await self.llm.generate_str(message=message)

        elif self.session_type == SessionType.REPORT:
            if not self.llm:
                return "Error: Report writer not initialized"
            return await self.llm.generate_str(message=message)

        elif self.session_type == SessionType.FULL_ANALYSIS:
            if not self.orchestrator:
                return "Error: Orchestrator not initialized"

            # Create comprehensive analysis task
            task = f"""Create a high-quality stock analysis report for {self.company_name} by following these steps:

            1. Use the EvaluatorOptimizerLLM component (named 'research_quality_controller') to gather high-quality
               financial data about {self.company_name}. This component will automatically evaluate
               and improve the research until it reaches EXCELLENT quality.

               Ask for:
               - Current stock price and recent movement
               - Latest quarterly earnings results and performance vs expectations
               - Recent news and developments

            2. Use the financial_analyst to analyze this research data and identify key insights.

            3. Use the report_writer to create a comprehensive stock report.

            The final report should be professional, fact-based, and include all relevant financial information.

            User request: {message}"""

            return await self.orchestrator.generate_str(
                message=task,
                request_params=RequestParams(model="gpt-4o-mini")
            )

    async def _process_by_type_streaming(
        self,
        message: str,
        stream_callback: Callable[[str], None]
    ) -> str:
        """Process message based on session type with streaming responses."""

        if self.session_type == SessionType.RESEARCH:
            if not self.streaming_llm:
                error_msg = "Error: Research agent streaming not initialized"
                stream_callback(error_msg)
                return error_msg

            return await self.streaming_llm.generate_str_streaming(
                message=message,
                stream_callback=stream_callback
            )

        elif self.session_type == SessionType.ANALYZE:
            if not self.streaming_llm:
                error_msg = "Error: Analyst agent streaming not initialized"
                stream_callback(error_msg)
                return error_msg

            return await self.streaming_llm.generate_str_streaming(
                message=message,
                stream_callback=stream_callback
            )

        elif self.session_type == SessionType.REPORT:
            if not self.streaming_llm:
                error_msg = "Error: Report writer streaming not initialized"
                stream_callback(error_msg)
                return error_msg

            return await self.streaming_llm.generate_str_streaming(
                message=message,
                stream_callback=stream_callback
            )

        elif self.session_type == SessionType.FULL_ANALYSIS:
            if not self.orchestrator or not self.streaming_llm:
                error_msg = "Error: Full analysis streaming not initialized"
                stream_callback(error_msg)
                return error_msg

            # For full analysis, we'll use a combination of streaming and non-streaming
            # First, send a message that we're starting the analysis
            stream_callback("Starting comprehensive financial analysis for " + self.company_name + "...\n\n")

            # Create comprehensive analysis task
            task = f"""Create a high-quality stock analysis report for {self.company_name} by following these steps:

            1. Use the EvaluatorOptimizerLLM component (named 'research_quality_controller') to gather high-quality
               financial data about {self.company_name}. This component will automatically evaluate
               and improve the research until it reaches EXCELLENT quality.

               Ask for:
               - Current stock price and recent movement
               - Latest quarterly earnings results and performance vs expectations
               - Recent news and developments

            2. Use the financial_analyst to analyze this research data and identify key insights.

            3. Use the report_writer to create a comprehensive stock report.

            The final report should be professional, fact-based, and include all relevant financial information.

            User request: {message}"""

            # For orchestrator, we can't easily stream, so we'll provide progress updates
            stream_callback("Researching financial data...\n")

            # Use the orchestrator (non-streaming)
            result = await self.orchestrator.generate_str(
                message=task,
                request_params=RequestParams(model="gpt-4o-mini")
            )

            # Send the final result
            stream_callback("\n\nFinal Analysis Report:\n\n")
            stream_callback(result)

            return result

    async def cleanup(self):
        """Clean up the session resources."""
        try:
            if self.research_agent:
                await self.research_agent.__aexit__(None, None, None)
            if self.analyst_agent:
                await self.analyst_agent.__aexit__(None, None, None)
            if self.report_writer:
                await self.report_writer.__aexit__(None, None, None)
            if self.agent_app:
                await self.agent_app.__aexit__(None, None, None)
        except Exception as e:
            print(f"Error during session cleanup for user {self.user_id}: {e}")


class FinancialSessionManager:
    """Manages financial analysis sessions for the WebSocket server."""

    def __init__(self):
        self.sessions: Dict[str, FinancialSession] = {}
        self.cleanup_interval = 3600  # Clean up inactive sessions every hour
        self.max_inactive_time = 7200  # Remove sessions inactive for 2 hours

    async def initialize(self):
        """Initialize the session manager."""
        # Start cleanup task
        asyncio.create_task(self._cleanup_task())
        
        # Ensure output directory exists
        os.makedirs("company_reports", exist_ok=True)

    async def get_or_create_session(
        self, 
        user_id: str, 
        session_type: SessionType, 
        company_name: str
    ) -> FinancialSession:
        """Get existing session or create a new one for the user."""
        session_key = f"{user_id}_{session_type.value}"
        
        if session_key in self.sessions:
            session = self.sessions[session_key]
            session.last_activity = datetime.now()
            return session

        # Create new session
        session_id = str(uuid.uuid4())
        session = FinancialSession(user_id, session_id, session_type)

        try:
            await session.initialize(company_name)
            self.sessions[session_key] = session
            return session
        except Exception as e:
            await session.cleanup()
            raise Exception(f"Failed to create {session_type.value} session for user {user_id}: {str(e)}")

    async def cleanup_session(self, user_id: str, session_type: SessionType):
        """Clean up a specific user session."""
        session_key = f"{user_id}_{session_type.value}"
        if session_key in self.sessions:
            session = self.sessions[session_key]
            await session.cleanup()
            del self.sessions[session_key]

    async def cleanup(self):
        """Clean up all sessions."""
        cleanup_tasks = []
        for session_key, session in self.sessions.items():
            cleanup_tasks.append(session.cleanup())

        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)

        self.sessions.clear()

    async def _cleanup_task(self):
        """Background task to clean up inactive sessions."""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)

                current_time = datetime.now()
                inactive_sessions = []

                for session_key, session in self.sessions.items():
                    time_since_activity = (
                        current_time - session.last_activity
                    ).total_seconds()
                    if time_since_activity > self.max_inactive_time:
                        inactive_sessions.append(session_key)

                # Clean up inactive sessions
                for session_key in inactive_sessions:
                    print(f"Cleaning up inactive session: {session_key}")
                    session = self.sessions[session_key]
                    await session.cleanup()
                    del self.sessions[session_key]

            except Exception as e:
                print(f"Error in cleanup task: {e}")

    def get_session_info(self, user_id: str, session_type: SessionType) -> Optional[dict]:
        """Get session information for a user and session type."""
        session_key = f"{user_id}_{session_type.value}"
        if session_key not in self.sessions:
            return None

        session = self.sessions[session_key]
        return {
            "user_id": session.user_id,
            "session_id": session.session_id,
            "session_type": session.session_type.value,
            "company_name": session.company_name,
            "created_at": session.created_at.isoformat(),
            "last_activity": session.last_activity.isoformat(),
            "message_count": len(session.message_history),
        }
